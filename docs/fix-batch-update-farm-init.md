# 修复批量资源更新接口的农场区块初始化问题

## 问题描述

在 `api/wallet/batch-update-resources` 接口中，当用户的 `farm_plots` 表没有数据时，原来的实现只会创建一个默认的农场区块，而不是完整的20个农场区块。这导致了以下问题：

1. **数据不完整**：用户只有1个农场区块，而不是标准的20个
2. **功能受限**：用户无法解锁其他农场区块，因为它们根本不存在
3. **数据不一致**：与 `api/farm/farm-plots` 接口的行为不一致

## 问题根因

在以下两个服务文件中，当检测到用户没有解锁的农场区块时，只创建了一个默认的农场区块：

- `src/services/batchResourceUpdateService.ts`
- `src/services/strictBatchResourceUpdateService.ts`

原来的代码：
```typescript
// 如果用户没有解锁的农场区块，自动初始化一个
if (farmPlots.length === 0) {
  const defaultFarmPlot = await FarmPlot.create({
    walletId,
    plotNumber: 1,
    level: 1,
    isUnlocked: true,
    // ... 其他属性
  }, { transaction });
  farmPlots = [defaultFarmPlot];
}
```

## 解决方案

### 修复策略

1. **使用配置驱动的初始化**：基于 `farm_configs` 表的配置来创建农场区块
2. **创建完整的20个农场区块**：确保数据完整性
3. **保持事务一致性**：在当前事务中完成初始化，避免事务管理问题
4. **遵循现有逻辑**：与 `farmPlotService.initializeUserFarmPlots` 方法保持一致

### 修复后的代码

```typescript
// 如果用户没有农场区块数据，需要初始化完整的20个农场区块
if (farmPlots.length === 0) {
  console.log(`🌱 用户 ${walletId} 没有农场区块数据，开始初始化20个农场区块...`);
  
  // 在当前事务中初始化农场区块，但需要先检查是否已存在
  const existingPlots = await FarmPlot.findAll({ where: { walletId }, transaction });
  
  if (existingPlots.length === 0) {
    // 获取当前激活的配置
    const { FarmConfigService } = require('./farmConfigService');
    const configs = await FarmConfigService.getCurrentConfig();
    
    if (configs.length === 0) {
      throw new Error('没有找到激活的农场配置，无法初始化农场区块');
    }
    
    // 获取等级1的配置
    const level1Config = configs.find((c: any) => c.grade === 1);
    if (!level1Config) {
      throw new Error('找不到等级1的配置数据');
    }
    
    // 创建20个农场区块
    const farmPlotsToCreate = [];
    for (let i = 1; i <= 20; i++) {
      const isFirstPlot = i === 1;
      const unlockCost = await FarmPlotCalculator.calculateUnlockCost(i);
      
      farmPlotsToCreate.push({
        walletId,
        plotNumber: i,
        level: 1,
        barnCount: isFirstPlot ? level1Config.cow : 0,
        milkProduction: isFirstPlot ? level1Config.production : 0,
        productionSpeed: isFirstPlot ? level1Config.speed : 100,
        unlockCost: unlockCost,
        upgradeCost: isFirstPlot ? level1Config.cost : 0,
        lastProductionTime: new Date(),
        isUnlocked: isFirstPlot,
        accumulatedMilk: 0
      });
    }
    
    await FarmPlot.bulkCreate(farmPlotsToCreate, { transaction });
    console.log(`✅ 成功在事务中初始化 ${farmPlotsToCreate.length} 个农场区块`);
  }
  
  // 重新查询已解锁的农场区块
  farmPlots = await FarmPlot.findAll({
    where: { walletId, isUnlocked: true },
    transaction
  });
  
  console.log(`✅ 找到 ${farmPlots.length} 个已解锁的农场区块`);
}
```

## 修复的文件

1. `src/services/batchResourceUpdateService.ts` - 两处修复
2. `src/services/strictBatchResourceUpdateService.ts` - 一处修复

## 修复后的行为

### 当用户没有农场区块数据时：

1. **自动初始化20个农场区块**：
   - plotNumber 1-20
   - 只有第一个农场区块（plotNumber=1）是解锁状态
   - 其他19个农场区块是未解锁状态

2. **基于配置表的初始化**：
   - 使用 `farm_configs` 表中 grade=1 的配置
   - 产量、牛舍数量、速度等都来自配置表
   - 解锁费用通过 `FarmPlotCalculator.calculateUnlockCost` 计算

3. **数据一致性**：
   - 与 `api/farm/farm-plots` 接口的初始化行为完全一致
   - 确保用户在不同接口中看到相同的数据结构

## 测试验证

创建了测试脚本 `scripts/test-batch-update-farm-init.js` 来验证修复：

1. 清理测试用户的农场区块数据
2. 调用批量资源更新接口
3. 验证是否正确初始化了20个农场区块
4. 验证第一个区块是否解锁，其他区块是否未解锁
5. 验证不会重复初始化

## 影响评估

### 正面影响：
- 修复了数据不完整的问题
- 提高了接口行为的一致性
- 用户体验更好，功能完整

### 风险评估：
- 低风险：修改只影响没有农场区块数据的用户
- 向后兼容：不影响已有数据的用户
- 事务安全：在现有事务中完成操作，保证数据一致性

## 部署建议

1. 在测试环境充分验证
2. 监控初始化过程的日志
3. 确认 `farm_configs` 表有正确的配置数据
4. 可以考虑在部署后运行测试脚本验证功能
